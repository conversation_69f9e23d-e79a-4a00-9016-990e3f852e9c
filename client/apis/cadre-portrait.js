import { message } from "antd";
import axios from "axios";
import { headers, http } from "client/tool/axios";
import { ucHost, ucPms, ucPms1 } from "./config";
// 查询班子成员 http://wiki.aidangqun.com/project/8?p=1966 40102021205-查询班子成员
export const getCsrf = (data) => {
  return http.get(`${ucHost}/pms-leader-team/get-list/${data}`);
};

// 新增班子成员 http://wiki.aidangqun.com/project/8?p=1967 40102021206-新增班子成员
export const addCsrf = (data) => {
  return http.post(`${ucHost}/pms-leader-team/save`, data);
};

// 删除班子成员 http://wiki.aidangqun.com/project/8?p=1968 40102021207-删除班子成员
export const deleteCsrf = (data) => {
  return http.get(`${ucHost}/pms-leader-team/delete/${data}`);
};

// 班子成员排序 http://wiki.aidangqun.com/project/8?p=1969 40102021208-班子成员排序
export const sortCsrf = (data) => {
  return http.post(`${ucHost}/pms-leader-team/sort`, data);
};

// 评测列表 http://wiki.aidangqun.com/project/8?p=1962   40102021201-评测列表
export const getCsrfList = (data) => {
  return http.get(`${ucPms}/eval/eval-list`, data);
};

export const getCsrfList1 = (data) => {
  return http.get(`${ucPms}/p-eval/eval-list`, data);
};
// https://wiki.aidangqun.com/project/8?p=3177
export const getCsrfList2 = (data) => {
  return http.get(`${ucPms}/p_eval/middle_eval_list`, data);
};
// 评测列表 http://wiki.aidangqun.com/project/8?p=1962   40102021201-评测列表
export const getCsrfList3 = (data) => {
  return http.get(`${ucPms1}/subordinate/eval-list`, data);
};
// 取消评测 http://wiki.aidangqun.com/project/8?p=1963 40102021202-取消评测
export const cancelCsrf = (data) => {
  return http.get(`${ucPms}/eval/cancel`, data);
};
export const cancelCsrf1 = (data) => {
  return http.get(`${ucPms}/p-eval/cancel`, data);
};
// 添加评测信息 http://wiki.aidangqun.com/project/8?p=1965
export const addEvalList = (data) => {
  return http.post(`${ucPms}/eval/add-eval-list`, data);
};
export const addEvalList1 = (data) => {
  return http.post(`${ucPms}/eval/add-eval-list`, data);
};

// 根据测评id查询单条数据 http://wiki.aidangqun.com/project/8?p=1964 40102021203-根据组织id查询评测人员信息
export const getCsrfById = (data) => {
  return http.get(`${ucPms}/eval/eval-user-list`, data);
};

// 干部信息管理列表 http://wiki.aidangqun.com/project/8?p=1970 402020203-干部信息单位人员查询/pms-org-user/list
export const getOrgUserList = (data) => {
  return http.get(`${ucHost}/pms-org-user/list`, data);
};

// 删除单条干部信息 http://wiki.aidangqun.com/project/8?p=1973 402020205-删除
export const deleteOrgUser = (data) => {
  return http.get(`${ucHost}/pms-org-user/remove`, data);
};

//获取上级机构
export const getOrgNameList = (data) => {
  return http.get(`${ucHost}/org/org-name-list`, data);
};
// 行政机构管理参数字典 http://wiki.aidangqun.com/project/8?p=1976 402020207-数据字典列表/uc/op/list
export const getOpList = (data) => {
  return http.get(`${ucHost}/uc/op/list`, data);
};

// 行政机构组织资料详情 http://wiki.aidangqun.com/project/8?p=1975  402020206-组织资料/org/find-by-id/{id}
export const getNewOrgInfo = (data) => {
  return http.get(`${ucHost}/org/find-by-id/${data}`);
};

//行政机构查询领导干部列表 http://wiki.aidangqun.com/project/8?p=1977   402020208-查询领导干部人员列表/uc/user/leader-cadre-users
export const getLeaderCadreUsers = (data) => {
  return http.get(`${ucHost}/uc/user/leader-cadre-users`, data);
};

//行政机构新增组织 402020202-新建组织/org/add http://wiki.aidangqun.com/project/8?p=1961
export const addOrg = (data) => {
  return http.post(`${ucHost}/org/add`, data);
};

//行政机构删除组织 http://wiki.aidangqun.com/project/8?p=1973 402020204-删除
export const deleteOrgStatus = (data) => {
  return http.get(`${ucHost}/org/delete`, data);
};

//发起测评 http://wiki.aidangqun.com/project/8?p=1992  4010202120903-根据组织id查询测评人
export const getEvalUserList = (data) => {
  return http.get(`${ucPms}/pms/find-appraisal-user`, data);
};

//发布测评 http://wiki.aidangqun.com/project/8?p=1991 4010202120902-发起测评
export const addEval = (data) => {
  return http.post(`${ucPms}/pms/initiate-evaluation`, data);
};
// 4010202121101-得到测评表生成二级码 http://wiki.aidangqun.com/project/8?p=1997
export const getQrCodeInfo = (data) => {
  return http.post(`${ucPms}/eval/get-qr-code-info`, data);
};
// 职务查询/pms-job-management/getList http://wiki.aidangqun.com/project/8?p=2000
export const getPositionList = (params) => {
  return http.get(`${ucHost}/pms-job-management/getList`, params);
};
// 新增职务http://wiki.aidangqun.com/project/8?p=2001
export const addPosition = (params) => {
  return http.post(`${ucHost}/pms-job-management/add-update`, params);
};
// 职务存在验证http://wiki.aidangqun.com/project/8?p=2002
export const validatePosition = (params) => {
  return http.post(`${ucHost}/pms-job-management/validate`, params);
};
// 修改职务信息http://wiki.aidangqun.com/project/8?p=2003
export const updatePosition = (params) => {
  return http.post(`${ucHost}/pms-job-management/change-status`, params);
};
// 修改职务信息http://wiki.aidangqun.com/project/8?p=2003
export const getPositionTypeList = (params) => {
  return http.get(`${ucHost}/pms-job-management/getTypeList`, params);
};
// 4010202120534-删除职务/pms-job-management/del
export const deletePosition = (params) => {
  return http.get(`${ucHost}/pms-job-management/del`, params);
};

// 机构和人员信息 https://wiki.aidangqun.com/project/8?p=2510
export const findAppraisalUser = (params) => {
  return http.get(`${ucPms}/pms/find_appraisal_user_v2`, params);
};
export const findAppraisalUser1 = (params) => {
  return http.get(`${ucPms}/p-eval/find_appraisal_user`, params);
};
// 机构和人员信息 https://wiki.aidangqun.com/project/8?p=2510
export const findAppraisalUser2 = (params) => {
  return http.get(`${ucPms}/p_eval/find_appraisal_middle_user`, params);
};
// 机构和人员信息 https://wiki.aidangqun.com/project/8?p=2510
export const findAppraisalUser3 = (params) => {
  return http.get(`${ucPms1}/subordinate/find_org`, params);
};
// 测评详情 https://wiki.aidangqun.com/project/8?p=2532
export const getEvalDetail = (params) => {
  return http.get(`${ucPms}/pms/find_evaluation`, params);
};
// 测评详情 https://wiki.aidangqun.com/project/8?p=2532
export const getEvalDetail3 = (params) => {
  return http.get(`${ucPms}/p_eval/find_middle_evaluation`, params);
};
export const getEvalDetail2 = (params) => {
  return http.get(`${ucPms}/p-eval/find_evaluation`, params);
};
// 根据姓名查询 https://wiki.aidangqun.com/project/8?p=2511
export const findByUser = (params) => {
  return http.get(`${ucPms}/pms/find_user`, params);
};
// 根据姓名查询 https://wiki.aidangqun.com/project/8?p=2511
export const findByUser1 = (params) => {
  return http.get(`${ucPms}/p_eval/find_user`, params);
};
// 根据姓名查询 https://wiki.aidangqun.com/project/8?p=2511
export const findByOrg = (params) => {
  return http.get(`${ucPms}/pms/find_org`, params);
};
// 发布测评 https://wiki.aidangqun.com/project/8?p=2512
export const initiateEvaluation = (params) => {
  return http.post(`${ucPms}/pms/initiate_evaluation_v2`, params);
};
export const initiateEvaluation1 = (params) => {
  return http.post(`${ucPms}/p-eval/initiate_evaluation`, params);
};
// 发布测评 https://wiki.aidangqun.com/project/8?p=2512
export const initiateEvaluation2 = (params) => {
  return http.post(`${ucPms}/p_eval/initiate_middle_evaluation`, params);
};
// 发布测评 https://wiki.aidangqun.com/project/8?p=2512
export const initiateEvaluation3 = (params) => {
  return http.post(`${ucPms}/pms/initiate_evaluation_v3`, params);
};
// 发布测评 https://wiki.aidangqun.com/project/8?p=3566
export const initiateEvaluation4 = (params) => {
  return http.post(`${ucPms1}/subordinate/initiate_evaluation`, params);
};

// 二维码 https://wiki.aidangqun.com/project/8?p=2516
export const getQrCodeInfoV2 = (params) => {
  return http.get(`${ucPms}/pms/get-evaluation-qrcode`, params);
};
// 二维码 https://wiki.aidangqun.com/project/8?p=2516
export const getQrCodeInfoV5 = (params) => {
  return http.get(`${ucPms1}/subordinate/get_evaluation_qrcode`, params);
};
export const getQrCodeInfoV3 = (params) => {
  return http.get(`${ucPms}/p-eval/get-evaluation-qrcode`, params);
};
export const getQrCodeInfoV4 = (params) => {
  return http.get(`${ucPms}/p_eval/get_middle_evaluation_qrcode`, params);
};
// 设置测评二维码 https://wiki.aidangqun.com/project/8?p=2531
export const setQrCodeInfo = (params) => {
  return http.post(`${ucPms}/pms/set-evaluation-qrcode`, params);
};
// 测评详情 https://wiki.aidangqun.com/project/8?p=2587
export const getEvalDetail1 = (params) => {
  return http.get(`${ucPms}/pms/find_appraisal_user_v3`, params);
};
//  40102021501-查询干部信息 https://wiki.aidangqun.com/project/8?p=2878
export const getQueryCadreInfo = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/query_cadre_info`, params);
};
//根据code查询字典 https://wiki.aidangqun.com/project/8?p=2819
export const queryByCode = (params) => {
  return http.get(`${ucPms}/cadre_select/code`, params);
};
//  获取机构全称（/cadre_info_manage/org_name）https://wiki.aidangqun.com/project/8?p=3524
export const queryOrgName = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/org_name`, params);
};
// 40102021502-籍贯出生地查询/cadre_info_manage/query_dicitem_area https://wiki.aidangqun.com/project/8?p=2882
export const queryDicitemArea = (params) => {
  return http.post(`${ucPms}/cadre_info_manage/query_dicitem_area`, params);
};
// 40102021503-新增（修改）干部信息-基础信息/cadre_info_manage/add_or_update_cadre_info https://wiki.aidangqun.com/project/8?p=2886
export const addOrUpdateCadreInfo = (params) => {
  return http.post(
    `${ucPms}/cadre_info_manage/add_or_update_cadre_info`,
    params
  );
};
// 40102021505-新增（修改）干部教育信息/cadre_info_manage/add_or_update_education_info https://wiki.aidangqun.com/project/8?p=2886
export const addOrUpdateEducationInfo = (params) => {
  return http.post(
    `${ucPms}/cadre_info_manage/add_or_update_education_info`,
    params
  );
};
// 导出lrmx任免审批表（/read/export-cadre-info-lrmx）
export const exportCadreInfoLrmx = (params, fileName) => {
  return fileDownloadGet(
    `${ucPms}/read/export-cadre-info-lrmx`,
    params,
    fileName
  );
};
// 402020201014-导出word任免审批表(/read/export-cadre-info) https://wiki.aidangqun.com/project/8?p=3666
export const apiExportCadreInfo = (params, fileName = "干部任免审批表.doc") => {
  return fileDownloadGet(`${ucPms}/read/export-cadre-info`, params, fileName);
};
// 40102021524-新增/修改，删除社会关系（/cadre_info_manage/add_or_update_relationship_graph）

export const addOrUpdateRelationshipGraph = (params) => {
  return http.post(
    `${ucPms}/cadre_info_manage/add_or_update_relationship_graph`,
    params
  );
};
// 40102021524-新增/修改，删除社会关系（/cadre_info_manage/add_or_update_relationship_graph）

// 40102021525-新增/修改，删除分管领域（/cadre_info_manage/add_or_update_charge_range）

export const addOrUpdateChargeRange = (params) => {
  return http.post(
    `${ucPms}/cadre_info_manage/add_or_update_charge_range`,
    params
  );
};
// 40102021506-新增（修改）干部简历信息/cadre_info_manage/add_or_update_work_info https://wiki.aidangqun.com/project/8?p=2886

export const addOrUpdateWorkInfo = (params) => {
  return http.post(
    `${ucPms}/cadre_info_manage/add_or_update_work_info`,
    params
  );
};

//  40102021517-条件查询干部列表/cadre_info_manage/query_cadre_list https://wiki  .aidangqun.com/project/8?p=2903
export const queryCadreList = (params) => {
  return http.post(`${ucPms}/cadre_info_manage/query_cadre_list`, params);
};
//40102021518-删除干部/cadre_info_manage/delete_cadre_info https://wiki.aidangqun.com/project/8?p=2906
export const deleteUser = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/delete_cadre_info`, params);
};
//  40102021519-职位查询/cadre_info_manage/query_job_list https://wiki.aidangqun.com/project/8?p=2908

export const queryJobList = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/query_job_list`, params);
};
// 40202021520-干部调任/cadre_info_manage/transfer_cadre
export const transferCadre = (params) => {
  return http.post(`${ucPms}/cadre_info_manage/transfer_cadre`, params);
};
// 40102021507-条件查询表扬表彰信息列表/cadre_info_manage/query_commend_info_list https://wiki.aidangqun.com/project/8?p=2891
export const queryCommendInfoList = (params) => {
  return http.post(
    `${ucPms}/cadre_info_manage/query_commend_info_list`,
    params
  );
};
// 40102021508-新增或修改表扬表彰信息/cadre_info_manage/add_or_update_commend_info https://wiki.aidangqun.com/project/8?p=2893
export const addOrUpdateCommendInfo = (params) => {
  return http.post(
    `${ucPms}/cadre_info_manage/add_or_update_commend_info`,
    params
  );
};
// 40102021509-删除表扬表彰信息/cadre_info_manage/delete_commend_info https://wiki.aidangqun.com/project/8?p=2894
export const deleteCommendInfo = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/delete_commend_info`, params);
};
// 40102021515-表扬表彰清单数据导出/cadre_info_manage/export_commend_list
export const exportCommendList = (params) => {
  return fileDownload(`${ucPms}/cadre_info_manage/export_commend_list`, params);
};
// 40102021511-条件查询处分信息列表/cadre_info_manage/query_punish_info_list https://wiki.aidangqun.com/project/8?p=2895
export const queryPunishInfoList = (params) => {
  return http.post(`${ucPms}/cadre_info_manage/query_punish_info_list`, params);
};
// 40102021513-新增或更新处分信息/cadre_info_manage/add_or_update_punish_info https://wiki.aidangqun.com/project/8?p=2898
export const addOrUpdatePunishInfo = (params) => {
  return http.post(
    `${ucPms}/cadre_info_manage/add_or_update_punish_info`,
    params
  );
};
// 40102021516-处分清单数据导出/cadre_info_manage/export_punish_list
export const exportPunishList = (params) => {
  return fileDownload(`${ucPms}/cadre_info_manage/export_punish_list`, params);
};

// 40102021504-班子下职务查询/cadre_info_manage/query_job_info https://wiki.aidangqun.com/project/8?p=2884

export const queryJobInfo = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/query_job_info`, params);
};

// 40102021510-查询表扬表彰信息详情/cadre_info_manage/query_commend_info
export const queryCommendInfo = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/query_commend_info`, params);
};
// 40102021514-删除处分信息/cadre_info_manage/delete_punish_info
export const deletePunishInfo = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/delete_punish_info`, params);
};
//  40102021512-查询处分信息详情//cadre_info_manage/query_punish_info
export const queryPunishInfo = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/query_punish_info`, params);
};
// 40202021521-班子或组织判断/cadre_info_manage/query_org_type
export const queryOrgType = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/query_org_type`, params);
};
// 干部排序-/cadre_info_manage/update_leader_seq https://wiki.aidangqun.com/project/8?p=3667
export const updateLeaderSeq = (params) => {
  return http.post(`${ucPms}/cadre_info_manage/update_leader_seq`, params);
};
// 40202021522-删除工作信息/cadre_info_manage/delete_work_info
export const deleteWorkInfo = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/delete_work_info`, params);
};
// 40102021601-序列列表（/cadre_info_manage/query_group_list）
export const queryGroupList = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/query_group_list`, params);
};
// 40102021602-序列新增/编辑（/cadre_info_manage/add_or_update_group）
export const addOrUpdateGroup = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/add_or_update_group`, params);
};
// 40102021603-序列下人员（/cadre_info_manage/query_group_user）
export const queryGroupUser = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/query_group_user`, params);
};
//  40102021604-修改干部序列（/cadre_info_manage/update_user_group）
export const updateUserGroup = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/update_user_group`, params);
};
// 年度考核列表查询/cadre_info_manage/query_annual_eval_list
export const queryAnnualEvalList = (params) => {
  return http.post(`${ucPms}/cadre_info_manage/query_annual_eval_list`, params);
};
// 40102021523-干部头像上传/cadre_info_manage/upload_avatar
export const uploadAvatar = (params, queryHeader) => {
  return http.post(
    `${ucPms}/cadre_info_manage/upload_avatar`,
    params,
    queryHeader || headers()
  );
};
// 新增或更新年度考核/cadre_info_manage/ https://wiki.aidangqun.com/project/8?p=2993
export const addOrUpdateAnnualEval = (params) => {
  return http.post(
    `${ucPms}/cadre_info_manage/add_or_update_annual_eval`,
    params
  );
};
//  删除年度考核/cadre_info_manage/delete_annual_eval https://wiki.aidangqun.com/project/8?p=2994
export const deleteAnnualEval = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/delete_annual_eval`, params);
};
// 年度考核数据导出/cadre_info_manage/export_annual_eval_list  https://wiki.aidangqun.com/project/8?p=2995
export const exportAnnualEval = (params) => {
  return fileDownload(
    `${ucPms}/cadre_info_manage/export_annual_eval_list`,
    params
  );
};
//  模板下载/cadre_info_manage/download_template
export const downloadTemplate = (params) => {
  return fileDownloadGet(
    `${ucPms}/cadre_info_manage/download_template`,
    params
  );
};
// 现实表现数据导入/pms-leader-screen/import https://wiki.aidangqun.com/project/8?p=2985
export const importData = (params) => {
  return http.post(`${ucPms}/pms-leader-screen/import`, params);
};
// 年度考核数据导入/cadre_info_manage/import_annual_eval_list https://wiki.aidangqun.com/project/8?p=2996
export const importAnnualEval = (params) => {
  return http.post(
    `${ucPms}/cadre_info_manage/import_annual_eval_list`,
    params
  );
};
// 导入失败数据下载/pms-leader-screen/download_import_fail_data https://wiki.aidangqun.com/project/8?p=3007
export const downloadImportFailData = (params) => {
  return fileDownload(
    `${ucPms}/cadre_info_manage/download_import_fail_data`,
    params
  );
};
//  现实表现列表查询/cadre_info_manage/query_major_event_list https://wiki.aidangqun.com/project/8?p=2977
export const queryMajorEventList = (params) => {
  return http.post(`${ucPms}/cadre_info_manage/query_major_event_list`, params);
};
// 新增或更新现实表现/cadre_info_manage/add_or_update_major_event https://wiki.aidangqun.com/project/8?p=2979
export const addOrUpdateMajorEvent = (params) => {
  return http.post(
    `${ucPms}/cadre_info_manage/add_or_update_major_event`,
    params
  );
};
// 删除现实表现/cadre_info_manage/delete_major_event https://wiki.aidangqun.com/project/8?p=2980
export const deleteMajorEvent = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/delete_major_event`, params);
};
// 现实表现数据导出/cadre_info_manage/export_major_event_list https://wiki.aidangqun.com/project/8?p=2982
export const exportMajorEvent = (params) => {
  return fileDownload(
    `${ucPms}/cadre_info_manage/export_major_event_list`,
    params
  );
};
//  查询现实表现详情/cadre_info_manage/query_major_event_detail https://wiki.aidangqun.com/project/8?p=2978
export const queryMajorEventDetail = (params) => {
  return http.get(
    `${ucPms}/cadre_info_manage/query_major_event_detail`,
    params
  );
};
// 查询年度考核详情/cadre_info_manage/query_annual_eval_detail
export const queryAnnualEvalDetail = (params) => {
  return http.get(
    `${ucPms}/cadre_info_manage/query_annual_eval_detail`,
    params
  );
};

export const download = (blob, fileName) => {
  const el = document.createElement("a");
  const href = window.URL.createObjectURL(blob); // 创建 URL 对象
  el.href = href;
  el.target = "_blank";
  el.style.display = "none";
  el.download = fileName;
  document.body.appendChild(el);
  el.click();
  document.body.removeChild(el); // 下载完成移除元素
  window.URL.revokeObjectURL(href); // 释放掉blob对象
};
// 直接生成a标签下载
export const fileDownloadA = (href) => {
  const el = document.createElement("a");
  el.href = href;
  el.target = "_blank";
  el.style.display = "none";
  document.body.appendChild(el);
  el.click();
  document.body.removeChild(el);
};
/**
 * 新开发的页面不建议调用该方法，请使用FileDownload组件
 * 该方法是为了兼容之前直接写在a标签onClick上的下载方式
 * @param {string} filePath ---必传 文件url
 * @param {string} fileName  文件名  请加上文件后缀  不传会从后端返回中获取
 * @param {boolean} multiple  多文件打包下载
 */
export const fileDownload = (filePath, data, fileName, multiple = false) => {
  return new Promise((resolve, reject) => {
    const customHeaders = {
      ...headers(),
      "Content-Type": "application/json",
    };
    // 如果是融合商城请求，则需要在请求头中放入access_key
    if (filePath.indexOf("/score/mall") !== -1) {
      customHeaders.access_key = accessKey;
    }
    axios
      .post(filePath, data, {
        headers: customHeaders,
        responseType: "blob",
      })
      .then((res) =>
        dealResponeFile({
          response: res,
          fileName,
          multiple,
          reject,
          resolve,
        })
      )
      .catch((err) => {
        reject(err);
        message.error(err.message || "网络错误");
      });
  });
};
export const fileDownloadGet = (filePath, data, fileName, multiple = false) => {
  return new Promise((resolve, reject) => {
    const customHeaders = {
      ...headers(),
      "Content-Type": "application/json",
    };
    // 如果是融合商城请求，则需要在请求头中放入access_key
    if (filePath.indexOf("/score/mall") !== -1) {
      customHeaders.access_key = accessKey;
    }
    axios
      .get(filePath, {
        params: data,
        headers: customHeaders,
        responseType: "blob",
      })
      .then((res) => {
        console.log("🚀 ~ returnnewPromise ~ res:", res);
        return dealResponeFile({
          response: res,
          fileName,
          multiple,
          reject,
          resolve,
        });
      })
      .catch((err) => {
        reject(err);
        message.error(err.message || "网络错误");
      });
  });
};

const dealResponeFile = ({ response, fileName, multiple, resolve, reject }) => {
  console.log("🚀 ~ dealResponeFile ~ response:", response);
  if (response.status === 200) {
    //处理文件 1. 多文件下载直接返回data 2.单个文件下载设置文件名，通过A标签下载
    const handleDownload = () => {
      if (multiple) {
        resolve();
        //多文件压缩下载
        return;
      }
      //未设置文件名则从response.headers中获取文件名
      // 后端必须配置Access-Control-Expose-Headers：Content-Disposition
      if (!fileName) {
        try {
          // 尝试解析content-disposition 后端必须配置
          const contentDisp = response.headers["content-disposition"];
          let fileNameArr = contentDisp.split("filename=");
          console.log("🚀 ~ handleDownload ~ fileNameArr:", fileNameArr);
          if (fileNameArr[1]) {
            fileNameArr[1] = fileNameArr[1].replace(/'|"/g, "");
            // 替换火狐浏览器中，无法识别的包含空格文件名，把文本空格替换为编码空格
            fileNameArr[1] = fileNameArr[1].replace(" ", "%20");
            fileName = decodeURIComponent(fileNameArr[1]);
          }
        } catch (error) { }
        if (!fileName) {
          fileName = "未知文件";
        }
      }
      const blob = new Blob([response.data], {
        // type: '"application/ynd.ms-excel;charset=UTF-8"',
        type: "application/octet-stream;charset=UTF-8",
      }); // 创建Blob实例
      //1 file-saver 保存文件  未验证
      // FileSaver.saveAs(blob, fileName)
      //2 a标签保存
      download(blob, fileName);
      resolve();
    };
    if (response.data.type === "application/json") {
      // 如果为json类型，则可能是下载错误，尝试解析后提示信息
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const res = JSON.parse(e.target.result);
          if (res.code !== 0) {
            message.error(res.message || "文件下载错误");
          }
        } catch (error) {
          // 解析错误则认为依然是文件
          reject();

          handleDownload();
        }
      };
      reader.readAsText(response.data);
    } else {
      handleDownload();
    }
  } else {
    reject();

    message.error(response.statusText || "系统错误");
  }
};
// 40102021701-用户管理分页（/sys_user/get_user_page）https://wiki.aidangqun.com/project/8?p=3073
export const getUserPage = (params) => {
  return http.get(`${ucPms}/sys_user/get_user_page`, params);
};
// 40102021719-系统用户单个查询（/sys_user/get_user）https://wiki.aidangqun.com/project/8?p=3100
export const getUser = (params) => {
  return http.get(`${ucPms}/sys_user/get_user`, params);
};
// ***********-添加系统用户（/sys_user/add_user）https://wiki.aidangqun.com/project/8?p=3074
export const addUser = (params) => {
  return http.post(`${ucPms}/sys_user/add_update_user`, params);
};
//  ***********-添加/编辑系统用户（/sys_user/add_user）https://wiki.aidangqun.com/project/8?p=3074
export const editUser = (params) => {
  return http.post(`${ucPms}/sys_user/edit_user`, params);
};
// ***********-获取账号（/sys_user/get_account）https://wiki.aidangqun.com/project/8?p=3075
export const getAccount = (params) => {
  return http.get(`${ucPms}/sys_user/get_account`, params);
};
// ***********-更新系统用户状态（/sys_user/update_user_status）https://wiki.aidangqun.com/project/8?p=3076
export const updateUserStatus = (params) => {
  return http.get(`${ucPms}/sys_user/update_user_status`, params);
};
//  ***********-用户权限分页（/sys_user/get_user_auth_page）https://wiki.aidangqun.com/project/8?p=3078
export const getUserAuthPage = (params) => {
  return http.get(`${ucPms}/sys_user/get_user_auth_page`, params);
};
// ***********-新增用户组织权限（/sys_user/add_user_org） https://wiki.aidangqun.com/project/8?p=3080
export const addUserOrg = (params) => {
  return http.post(`${ucPms}/sys_user/add_user_org`, params);
};
//  ***********-单个/批量 移除用户权限（/sys_user/del_user_auth）https://wiki.aidangqun.com/project/8?p=3079
export const delUserAuth = (params) => {
  return http.post(`${ucPms}/sys_user/del_user_auth`, params);
};
// ***********-修改系统用户密码（/sys_user/update_user_pwd）https://wiki.aidangqun.com/project/8?p=3077
export const updateUserPwd = (params, config) => {
  return http.get(`${ucPms}/sys_user/update_user_pwd`, params, config);
};
// 40102021710-用户机构下角色-点击编辑权限数据回显（/sys_user/get_user_org_role）https://wiki.aidangqun.com/project/8?p=3082
export const getUserOrgRole = (params) => {
  return http.post(`${ucPms}/sys_user/get_user_org_role`, params);
};
// 40102021709-新增用户组织角色权限（/sys_user/add_user_org_role） https://wiki.aidangqun.com/project/8?p=3081
export const addUserOrgRole = (params) => {
  return http.post(`${ucPms}/sys_user/add_user_org_role`, params);
};
//  40102021712-角色列表（/sys_user/get_role_list）https://wiki.aidangqun.com/project/8?p=3084
export const getRoleList = (params) => {
  return http.get(`${ucPms}/sys_user/get_role_list`, params);
};
// 40102021713-角色分页（/sys_user/get_role_page） https://wiki.aidangqun.com/project/8?p=3085
export const getRolePag = (params) => {
  return http.get(`${ucPms}/sys_user/get_role_page`, params);
};
// 40102021714-新增/编辑角色(/sys_user/add_update_role)
export const addUpdateRole = (params) => {
  return http.post(`${ucPms}/sys_user/add_update_role`, params);
};
// 40102021715-角色单个/批量 停用或者启用（/sys_user/update_role_status）https://wiki.aidangqun.com/project/8?p=3087
export const updateRoleStatus = (params) => {
  return http.post(`${ucPms}/sys_user/update_role_status`, params);
};
// 40102021716-删除角色(/sys_user/del_role) https://wiki.aidangqun.com/project/8?p=3088
export const delRole = (params) => {
  return http.post(`${ucPms}/sys_user/del_role`, params);
};
// 干部任用记录分页（/cadre_info_manage/query_assign_list ）https://wiki.aidangqun.com/project/8?p=3132
export const queryAssignList = (params) => {
  return http.post(`${ucPms}/cadre_info_manage/query_assign_list`, params);
};
// 干部任用记录新增/修改（/cadre_info_manage/add_or_update_assign） https://wiki.aidangqun.com/project/8?p=3133
export const addOrUpdateAssign = (params) => {
  return http.post(`${ucPms}/cadre_info_manage/add_or_update_assign`, params);
};
// 干部任用记录删除（/cadre_info_manage/delete_assign）https://wiki.aidangqun.com/project/8?p=3134
export const deleteAssign = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/delete_assign`, params);
};
// 干部任用记录导出（/cadre_info_manage/export_assign_list）https://wiki.aidangqun.com/project/8?p=3135
export const exportAssignList = (params) => {
  return fileDownload(`${ucPms}/cadre_info_manage/export_assign_list`, params);
};
// 干部任用记录数据导入（/cadre_info_manage/import_assign_list）https://wiki.aidangqun.com/project/8?p=3136
export const importAssignList = (params) => {
  return http.post(`${ucPms}/cadre_info_manage/import_assign_list`, params);
};
// 信访举报情况查询（/complaint_report/query） https://wiki.aidangqun.com/project/8?p=3375
export const queryComplaintReport = (params) => {
  return http.get(`${ucPms}/complaint_report/query`, params);
};
// 信访举报情况添加或更新 （/complaint_report/add_or_update） https://wiki.aidangqun.com/project/8?p=3376
export const addOrUpdateComplaintReport = (params) => {
  return http.post(`${ucPms}/complaint_report/add_or_update`, params);
};
// 信访举报情况删除 （/complaint_report/delete）https://wiki.aidangqun.com/project/8?p=3377
export const deleteComplaintReport = (params) => {
  return http.delete(`${ucPms}/complaint_report/delete`, params);
};
// 信访举报情况导出 （/complaint_report/export）https://wiki.aidangqun.com/project/8?p=3378
export const exportComplaintReport = (params) => {
  return fileDownload(
    `${ucPms}/complaint_report/export`,
    params,
    "信访举报情况.xlsx"
  );
};
// 信访举报情况导入 （/complaint_report/import https://wiki.aidangqun.com/project/8?p=3380
export const importComplaintReport = (params) => {
  return http.post(`${ucPms}/complaint_report/import`, params);
};
// 信访举报情况导出错误信息 （/complaint_report/export_fail）https://wiki.aidangqun.com/project/8?p=3379
export const exportFailComplaintReport = (params) => {
  return fileDownload(
    `${ucPms}/complaint_report/export_fail`,
    params,
    "信访举报情况导入失败信息.xlsx"
  );
};
// 巡视巡察评价查询（/patrol/query）https://wiki.aidangqun.com/project/8?p=3384
export const queryPatrol = (params) => {
  return http.get(`${ucPms}/patrol/query`, params);
};
// 巡视巡察评价添加或更新（/patrol/add_or_update）https://wiki.aidangqun.com/project/8?p=3385
export const addOrUpdatePatrol = (params) => {
  return http.post(`${ucPms}/patrol/add_or_update`, params);
};
// 巡视巡察评价姓名批次重复检查（/patrol/check）https://wiki.aidangqun.com/project/8?p=3390
export const checkPatrol = (params) => {
  return http.get(`${ucPms}/patrol/check`, params);
};
// 巡视巡察删除（/patrol/delete）https://wiki.aidangqun.com/project/8?p=3386
export const deletePatrol = (params) => {
  return http.delete(`${ucPms}/patrol/delete`, params);
};
// 巡视巡察数据导出（/patrol/export）https://wiki.aidangqun.com/project/8?p=3387
export const exportPatrol = (params) => {
  return fileDownload(`${ucPms}/patrol/export`, params, "巡视巡察评价.xlsx");
};
// 巡视巡察数据导入（/patrol/import）https://wiki.aidangqun.com/project/8?p=3389
export const importPatrol = (params) => {
  return http.post(`${ucPms}/patrol/import`, params);
};
// 巡视巡察导出错误信息（/patrol/export_fail ）https://wiki.aidangqun.com/project/8?p=3388
export const exportFailPatrol = (params) => {
  return fileDownload(
    `${ucPms}/patrol/export_fail`,
    params,
    "巡视巡察导入失败信息.xlsx"
  );
};
// 个人事项核查查询（/matters_check/query）https://wiki.aidangqun.com/project/8?p=3393
export const queryMattersCheck = (params) => {
  return http.get(`${ucPms}/matters_check/query`, params);
};
// 个人事项核查添加或更新（/matters_check/add_or_update）https://wiki.aidangqun.com/project/8?p=3394
export const addOrUpdateMattersCheck = (params) => {
  return http.post(`${ucPms}/matters_check/add_or_update`, params);
};
// 个人事项核查删除（/matters_check/delete）https://wiki.aidangqun.com/project/8?p=3395
export const deleteMattersCheck = (params) => {
  return http.delete(`${ucPms}/matters_check/delete`, params);
};
// 个人事项核查导出（/matters_check/export）https://wiki.aidangqun.com/project/8?p=3396
export const exportMattersCheck = (params) => {
  return fileDownload(
    `${ucPms}/matters_check/export`,
    params,
    "个人事项核查.xlsx"
  );
};
// 个人事项核查数据导入 （/matters_check/import）https://wiki.aidangqun.com/project/8?p=3398
export const importMattersCheck = (params) => {
  return http.post(`${ucPms}/matters_check/import`, params);
};
// 个人事项核查导出错误信息（/matters_check/export_fail）https://wiki.aidangqun.com/project/8?p=3397
export const exportFailMattersCheck = (params) => {
  return fileDownload(
    `${ucPms}/matters_check/export_fail`,
    params,
    "个人事项核查导入失败信息.xlsx"
  );
};
// 婚姻状况查询（/user_marriage/query）https://wiki.aidangqun.com/project/8?p=3406
export const queryUserMarriage = (params) => {
  return http.get(`${ucPms}/user_marriage/query`, params);
};
// 婚姻状况添加或修改（/user_marriage/add_or_update）https://wiki.aidangqun.com/project/8?p=3407
export const addOrUpdateUserMarriage = (params) => {
  return http.post(`${ucPms}/user_marriage/add_or_update`, params);
};
// 婚姻状况删除（/user_marriage/delete）https://wiki.aidangqun.com/project/8?p=3408
export const deleteUserMarriage = (params) => {
  return http.delete(`${ucPms}/user_marriage/delete`, params);
};
// 婚姻状况数据导出（/user_marriage/export）https://wiki.aidangqun.com/project/8?p=3409
export const exportUserMarriage = (params) => {
  return fileDownload(`${ucPms}/user_marriage/export`, params, "婚姻状况.xlsx");
};
// 婚姻状况数据导入（/user_marriage/import）https://wiki.aidangqun.com/project/8?p=3411
export const importUserMarriage = (params) => {
  return http.post(`${ucPms}/user_marriage/import`, params);
};
// 婚姻状况导出错误信息（/user_marriage/export_fail）https://wiki.aidangqun.com/project/8?p=3410
export const exportFailUserMarriage = (params) => {
  return fileDownload(
    `${ucPms}/user_marriage/export_fail`,
    params,
    "婚姻状况导入失败信息.xlsx"
  );
};
// 出国证件记录查询（/user_cert/query）https://wiki.aidangqun.com/project/8?p=3419
export const queryUserCert = (params) => {
  return http.get(`${ucPms}/user_cert/query`, params);
};
// 出国证件记录更新或添加（/user_cert/add_or_update） https://wiki.aidangqun.com/project/8?p=3420
export const addOrUpdateUserCert = (params) => {
  return http.post(`${ucPms}/user_cert/add_or_update`, params);
};
// 出国证件记录删除（ /user_cert/delete） https://wiki.aidangqun.com/project/8?p=3421
export const deleteUserCert = (params) => {
  return http.delete(`${ucPms}/user_cert/delete`, params);
};
// 出国证件记录数据导出（/user_cert/export）https://wiki.aidangqun.com/project/8?p=3422
export const exportUserCert = (params) => {
  return fileDownload(
    `${ucPms}/user_cert/export`,
    params,
    "出国(境)证件记录.xlsx"
  );
};
// 出国证件记录数据导入（/user_cert/import） https://wiki.aidangqun.com/project/8?p=3424
export const importUserCert = (params) => {
  return http.post(`${ucPms}/user_cert/import`, params);
};
// 出国证件记录导出错误信息（/user_cert/export_fail） https://wiki.aidangqun.com/project/8?p=3423
export const exportFailUserCert = (params) => {
  return fileDownload(
    `${ucPms}/user_cert/export_fail`,
    params,
    "出国(境)证件记录导入失败信息.xlsx"
  );
};
// 培训情况查询（/user_train/query） https://wiki.aidangqun.com/project/8?p=3458
export const queryUserTrain = (params) => {
  return http.get(`${ucPms}/user_train/query`, params);
};
// 培训情况添加或更新（/user_train /add_or_update）https://wiki.aidangqun.com/project/8?p=3459
export const addOrUpdateUserTrain = (params) => {
  return http.post(`${ucPms}/user_train/add_or_update`, params);
};
// 培训情况删除（/user_train/delete）https://wiki.aidangqun.com/project/8?p=3460
export const deleteUserTrain = (params) => {
  return http.delete(`${ucPms}/user_train/delete`, params);
};
// 培训情况数据导出（/user_train/export）https://wiki.aidangqun.com/project/8?p=3461
export const exportUserTrain = (params) => {
  return fileDownload(
    `${ucPms}/user_train/export`,
    params,
    "培训情况导出.xlsx"
  );
};
// 培训情况数据导入（/user_train/import）https://wiki.aidangqun.com/project/8?p=3463
export const importUserTrain = (params) => {
  return http.post(`${ucPms}/user_train/import`, params);
};
//  培训情况导出错误信息（/user_train/export_fail）https://wiki.aidangqun.com/project/8?p=3462
export const exportFailUserTrain = (params) => {
  return fileDownload(
    `${ucPms}/user_train/export_fail`,
    params,
    "培训情况导入失败信息.xlsx"
  );
};

// 班子回访查询
export const queryTeamVisit = (params) => {
  return http.get(`${ucPms}/user_marriage/query`, params);
};
// 班子回访添加或修改
export const addOrUpdateTeamVisit = (params) => {
  return http.post(`${ucPms}/user_marriage/add_or_update`, params);
};
// 班子回访删除
export const deleteTeamVisit = (params) => {
  return http.delete(`${ucPms}/user_marriage/delete`, params);
};
// 班子回访数据导出
export const exportTeamVisit = (params) => {
  return fileDownload(`${ucPms}/user_marriage/export`, params, "班子回访.xlsx");
};

// 年度述职查询
export const queryAnnualReport = (params) => {
  return http.get(`${ucPms}/user_marriage/query`, params);
};
// 年度述职添加或修改
export const addOrUpdateAnnualReport = (params) => {
  return http.post(`${ucPms}/user_marriage/add_or_update`, params);
};
// 年度述职删除
export const deleteAnnualReport = (params) => {
  return http.delete(`${ucPms}/user_marriage/delete`, params);
};
// 年度述职数据导出--- 暂时用不了,接口是错的，目前是后端还未写，所以前端先暂时注释掉
export const exportAnnualReport = (params) => {
  return fileDownload(`${ucPms}/user_marriage/export`, params, "年度述职.xlsx");
};

// 征求意见查询
export const queryTakeAdvice = (params) => {
  return http.get(`${ucPms}/user_marriage/query`, params);
};
// 征求意见添加或修改
export const addOrUpdateTakeAdvice = (params) => {
  return http.post(`${ucPms}/user_marriage/add_or_update`, params);
};
// 征求意见删除
export const deleteTakeAdvice = (params) => {
  return http.delete(`${ucPms}/user_marriage/delete`, params);
};
// 征求意见数据导出
export const exportTakeAdvice = (params) => {
  return fileDownload(`${ucPms}/user_marriage/export`, params, "征求意见.xlsx");
};

// 导入干部名册（/import/mesosphere-user-roster）
export const importMesosphereUserRoster = (params) => {
  return http.post(`${ucPms}/import/mesosphere-user-roster`, params);
};
//  导入职务信息（/eval-import/import_job）
export const importJob = (params) => {
  return http.post(`${ucPms}/eval-import/import_job`, params);
};
//  导入社会关系/eval-import/upload_relationship_graph https://wiki.aidangqun.com/project/8?p=3861&keyword=upload_relationship_graph
export const importRelationshipGraph = (params) => {
  return http.post(`${ucPms}/eval-import/upload_relationship_graph`, params);
};
//  导入分管领域（/eval-import/import_charge_range）
export const importChargeRange = (params) => {
  return http.post(`${ucPms}/eval-import/import_charge_range`, params);
};
//  导入分管领域失败
export const importChargeRangeFail = (params) => {
  return fileDownload(
    `${ucPms}/eval-import/download_charge_range_fail_data`,
    params,
    "分管领域导入失败信息.xlsx"
  );
};

// 导入表彰表扬
export const importReward = (params) => {
  return http.post(`${ucPms}/user_reward/import`, params);
};

// 导入表彰表扬-失败信息
export const importRewardFail = (params) => {
  return fileDownload(
    `${ucPms}/user_reward/download_fail_data`,
    params,
    "表彰表扬导入失败信息.xlsx"
  );
};

// 导入负面清单
export const importPunishment = (params) => {
  return http.post(`${ucPms}/user_punishment/import`, params);
};

// 导入负面清单-失败信息
export const importPunishmentFail = (params) => {
  return fileDownload(
    `${ucPms}/user_punishment/download_fail_data`,
    params,
    "负面清单导入失败信息.xlsx"
  );
};
// 导出班子回访-失败信息
export const importFailData = (params) => {
  return fileDownload(
    `${ucPms}/cadre_info_manage/download_import_fail_data`,
    params,
    "班子回访导入失败信息.xlsx"
  );
};

// 导入班子回访
export const importTeamVisit = (params) => {
  // return http.post(`${ucPms}/team_visit/import`, params);
  return http.post(`${ucPms}/visit/import`, params);
};

// 导入年度述职
export const importAnnualReport = (params) => {
  return http.post(`${ucPms}/import/import`, params);
};

// 导入征求意见
// export const importTakeAdvice = (params) => {
//   return http.post(`${ucPms}/take_advice/import`, params);
// };
// 导入征求意见
export const importTakeAdvice = (params) => {
  return http.post(`${ucPms}/import/otherSupervisor`, params);
};

//  *************-编辑组织/org/renovate
export const updateOrgRenovate = (data) => {
  return http.post(`${ucHost}/org/renovate`, data);
};
/** 导入干部任免审批表 https://wiki.aidangqun.com/project/35?p=3261 */
export const apiImport = (data = {}) =>
  http.post(`${ucPms}/read/upload-carde`, data, {
    ...headers(),
    "Content-Type": "multipart/form-data",
  });
/** 测评对比（/team_member/all_result）https://wiki.aidangqun.com/project/8?p=3647 */
export const getInspectionIndexEvalCompare = (data = {}) =>
  http.get(`${ucPms}/team_member/all_result`, data);

// 班子成员列表（/team_member/user_list）https://wiki.aidangqun.com/project/8?p=3648
export const getTeamMemberUserList = (data = {}) => {
  return http.get(`${ucPms}/team_member/user_list`, data);
};
//  *************-业绩能力口碑坐标分布/leader-screen/performance-point-screen https://wiki.aidangqun.com/project/8?p=1984
export const getLeaderScreenPerformancePoint = (data = {}) => {
  return http.get(`${ucPms}/leader-screen/performance-point-screen`, data);
};

// *************-金字塔/leader-screen/pyramid-screen https://wiki.aidangqun.com/project/8?p=1995
export const getLeaderScreenPyramid = (data = {}) => {
  return http.get(`${ucPms}/leader-screen/pyramid-screen`, data);
};
//  上传指数反馈单（/team_member/upload/photo）https://wiki.aidangqun.com/project/8?p=3649
export const uploadPhoto = (data = {}) => {
  return http.post(`${ucPms}/team_member/upload/photo`, data);
};

// 开始生成指数反馈单（/team_member/download/zip）https://wiki.aidangqun.com/project/8?p=3650
export const startZip = (data = {}) => {
  return http.get(`${ucPms}/team_member/download/zip`, data);
};
// 轮询文件生成状态（/team_member/download/zip）https://wiki.aidangqun.com/project/8?p=3650
export const loopDownloadZip = (data = {}) => {
  return http.get(`${ucPms}/team_member/download/status`, data);
};
// 开始下载（/team_member/download/zip）https://wiki.aidangqun.com/project/8?p=3650
export const downloadZip = (data = {}) => {
  return fileDownloadGet(`${ucPms}/team_member/download/file`, data);
};

// 上传干部指数要素分析（/team_member/upload/cadre/element）
export const uploadCadreElement = (data = {}) => {
  return http.post(`${ucPms}/team_member/upload/cadre/element`, data);
};
// 生成干部指数要素分析（word文档）（/team_member/download/cadre/element）
export const downloadCadreElement = (data = {}) => {
  return http.get(`${ucPms}/team_member/download/cadre/elements`, data);
};

//  确定干部指数要素分析（word文档）是否生成（/team_member/download/element/status）
export const downloadCadreElementStatus = (data = {}) => {
  return http.get(`${ucPms}/team_member/download/element/status`, data);
};
// 下载干部指数要素分析（word文档）（/team_member/download/element/file）
export const downloadCadreElementFile = (data = {}) => {
  return fileDownloadGet(`${ucPms}/team_member/download/element/file`, data);
};
// 下载pad数据包(/sand/sync_data) https://wiki.aidangqun.com:30443/project/8?p=3747
export const downloadSandSyncData = (data = {}) => {
  return http.get(`${ucPms}/sand/sync_data`, data);
};
//  下载pad数据包进度（/sand/sync_data_percent） https://wiki.aidangqun.com:30443/project/8?p=3748
export const syncDataPercent = (data = {}) => {
  return http.get(`${ucPms}/sand/sync_data_percent`, data);
};
// /下载测评数据包（/sand/sync_to_eval）https://wiki.aidangqun.com:30443/project/8?p=3749
export const syncToEval = (data = {}) => {
  return http.get(`${ucPms}/sand/sync_to_eval`, data);
};
// 上传测评结果数据包（/sand/sync_from_eval） https://wiki.aidangqun.com:30443/project/8?p=3767
export const syncFromEval = (data = {}) => {
  return http.post(`${ucPms}/sand/sync_from_eval`, data);
};
//1000101-班子回访列表查询/visit/list https://wiki.aidangqun.com:30443/project/37?p=3888
export const getTeamVisitList = (data = {}) => {
  return http.post(`${ucPms}/visit/list`, data);
};
// 1000102-班子回访详情/visit/detail https://wiki.aidangqun.com:30443/project/37?p=3889
export const getTeamVisitDetail = (data = {}) => {
  return http.get(`${ucPms}/visit/detail`, data);
};
//1000103-班子回访新增修改/visit/save https://wiki.aidangqun.com:30443/project/37?p=3890
export const saveTeamVisit = (data = {}) => {
  return http.post(`${ucPms}/visit/save`, data);
};
// 1000104-班子回访删除/visit/delete https://wiki.aidangqun.com:30443/project/37?p=3891
export const deleteTeamVisit1 = (data = {}) => {
  return http.get(`${ucPms}/visit/delete`, data);
};
//1000201-年度述职列表查询/annual_report/list https://wiki.aidangqun.com:30443/project/37?p=3893
export const getAnnualReportList = (data = {}) => {
  return http.post(`${ucPms}/annual_report/list`, data);
};
//1000202-年度述职详情/annual_report/detail https://wiki.aidangqun.com:30443/project/37?p=3894
export const getAnnualReportDetail = (data = {}) => {
  return http.get(`${ucPms}/annual_report/detail`, data);
};
// 1000203-年度述职新增修改/annual_report/save https://wiki.aidangqun.com:30443/project/37?p=3895
export const saveAnnualReport = (data = {}) => {
  return http.post(`${ucPms}/annual_report/save`, data);
};
// 1000204-年度述职删除/annual_report/delete https://wiki.aidangqun.com:30443/project/37?p=3896
export const deleteAnnualReport1 = (data = {}) => {
  return http.get(`${ucPms}/annual_report/delete`, data);
};
//1000301-征求意见列表查询/supervision/list https://wiki.aidangqun.com:30443/project/37?p=3898
export const getSupervisionList = (data = {}) => {
  return http.post(`${ucPms}/supervision/list`, data);
};
//1000302-征求意见详情/supervision/detail https://wiki.aidangqun.com:30443/project/37?p=3899
export const getSupervisionDetail = (data = {}) => {
  return http.get(`${ucPms}/supervision/detail`, data);
};
//1000303-征求意见新增修改/supervision/save https://wiki.aidangqun.com:30443/project/37?p=3900
export const saveSupervision = (data = {}) => {
  return http.post(`${ucPms}/supervision/save`, data);
};

//1000304-征求意见删除/supervision/delete https://wiki.aidangqun.com:30443/project/37?p=3901
export const deleteSupervision = (data = {}) => {
  return http.get(`${ucPms}/supervision/delete`, data);
};
//10000101-批量检测成员是否存在于其他序列/cadre_info_manage/check_user_group https://wiki.aidangqun.com/project/37?p=3915
export const checkUserGroup = (data = {}) => {
  return http.post(`${ucPms}/cadre_info_manage/check_user_group`, data);
};
//10000102-批量添加序列成员/cadre_info_manage/batch_add_group_user https://wiki.aidangqun.com/project/37?p=3916
export const batchAddGroupUser = (data = {}) => {
  return http.post(`${ucPms}/cadre_info_manage/batch_add_group_user`, data);
};
//10000103-干部移出序列/cadre_info_manage/delete_user_group https://wiki.aidangqun.com/project/37?p=3929
export const deleteUserGroup = (data = {}) => {
  return http.get(`${ucPms}/cadre_info_manage/delete_user_group`, data);
};
//  10000202-查询班子序列（/cadre_info_manage/query_org_group_list）
export const queryOrgGroupList = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/query_org_group_list`, params);
};
// 10000201-新增/修改班子序列（/cadre_info_manage/add_or_update_org_group）
export const addOrUpdateOrgGroup = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/add_or_update_org_group`, params);
};
// 10000204-查询班子序列下成员（/cadre_info_manage/query_group_org）
export const queryGroupOrg = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/query_group_org`, params);
};
//  10000205-修改班子序列成员的序列（/cadre_info_manage/update_org_group）
export const updateOrgGroup = (params) => {
  return http.get(`${ucPms}/cadre_info_manage/update_org_group`, params);
};
//10000103-干部移出序列/cadre_info_manage/delete_org_group https://wiki.aidangqun.com/project/37?p=3928
export const deleteOrgGroup = (data = {}) => {
  return http.get(`${ucPms}/cadre_info_manage/delete_org_group`, data);
};
//10000208-批量检测成员是否存在于其他序列/cadre_info_manage/check_org_group https://wiki.aidangqun.com/project/37?p=3931
export const checkOrgGroup = (data = {}) => {
  return http.post(`${ucPms}/cadre_info_manage/check_org_group`, data);
};  
//10000207-批量添加班子序列成员/cadre_info_manage/batch_add_group_org https://wiki.aidangqun.com/project/37?p=3930
export const batchAddGroupOrg = (data = {}) => {
  return http.post(`${ucPms}/cadre_info_manage/batch_add_group_org`, data);
};
//10000209-获取全部组织机构树/cadre_info_manage/org_data_tree https://wiki.aidangqun.com/project/37?p=3934
export const orgDataTree = (data = {}) => {
  return http.get(`${ucPms}/cadre_info_manage/org_data_tree`, data);
};

// 401020213-干部查询 https://wiki.aidangqun.com/project/8?p=2174
export const leaderQuery = (params) => {
  return http.post(`${ucPms}/pms-leader/query`, params);
};
//  10000601-干部查询 自定义导出字段查询 https://wiki.aidangqun.com/project/37?p=3936  
export const queryUserSelectFieid = (data = {}) => {
  return http.get(`${ucPms}/customize_fieid_exprot/query_user_select_fieid`, data);
};
// 10000602-干部查询 自定义字段的导出 https://wiki.aidangqun.com/project/37?p=3938
export const customizeExport = (params) => {
  return http.post(`${ucPms}/customize_fieid_exprot/customize_export_user_select_datas`, params);
};

//10000402-花名册 自定义字段导出 https://wiki.aidangqun.com/project/37?p=3922
export const customizeExportRoster = (params) => {
  return fileDownload(
    `${ucPms}/customize_fieid_exprot/customize_export_user_roster_datas`,
    params,
    "干部管理花名册.xlsx"
  );
};
// 10000401-自定义导出字段查询 https://wiki.aidangqun.com/project/37?p=3937
export const queryUserRosterFieid = (data = {}) => {
  return http.get(`${ucPms}/customize_fieid_exprot/query_user_roster_fieid`, data);
};
// 10000701-历史方案（全部成员可查看） https://wiki.aidangqun.com:30443/project/37?p=3945
export const mockAllList = (data = {}) => {
  return http.get(`${ucPms}/cadre_select/mock_all_list`, data);
};
// 10000702-动议名单导出 https://wiki.aidangqun.com:30443/project/37?p=3946
export const exportMotion = (params,fileName) => {
  return fileDownloadGet(`${ucPms}/cadre_select/export_motion`, params, fileName);
};