import {
  Button,
  Form,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Select,
  Table,
  message,
} from "antd";
import {
  addPosition,
  deletePosition,
  getPositionList,
  queryByCode,
  updatePosition,
} from "client/apis/cadre-portrait";
import OrgTree from "client/components/org-tree";
import { useEffect, useRef, useState } from "react";
import "./index.less";
const { Option } = Select;
const FormItem = Form.Item;

const PositionManagement = ({ form, history }) => {
  const { getFieldDecorator, getFieldsValue, resetFields } = form;
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [pageState, setPageState] = useState({
    org_type: -1,
    org_name: "",
    org_id: -1,
  });
  const [org_name, setOrgName] = useState("");
  const [org_ids, setOrgIds] = useState(["1"]);
  const [exportLoading, setExportLoading] = useState(false);
  const [position, setPosition] = useState({
    name: "",
  });
  const [buttonStatus, setButtonStatus] = useState({
    save: false,
    modalSave: false,
  });
  const [codeMap, setCodeMap] = useState({
    categoryOption: [],
    classificationOption: [], //分类
  });
  const [visible, setVisible] = useState(false);
  const orgRef = useRef(null);
  useEffect(() => {
    initCodeMap(96160, "categoryOption");
    initCodeMap(96400, "classificationOption");
    initData(org_ids[0]);
  }, []);

  const initCodeMap = async (code, key) => {
    const res = await queryByCode({
      code,
    });
    if (res.data.code === 0) {
      codeMap[key] = res.data.data;
      setCodeMap({
        ...codeMap,
      });
    }
  };

  const initData = async (org_id) => {
    try {
      setLoading(true);
      const res = await getPositionList({ org_id });
      if (res.data.code === 0) {
        // 提取 pms_job_list 数据
        const pmsJobList = res.data.data.flatMap((item) => item.pms_job_list);
        setData(pmsJobList);
      } else {
        // 如果返回的 code 不为 0，说明有错误发生
        message.error(res.data.message || "获取数据失败");
      }
    } catch (error) {
      // 捕获网络请求或其他错误
      console.error("Error fetching data:", error);
      message.error("网络请求失败，请稍后再试");
    } finally {
      // 无论成功还是失败，最后都关闭加载状态
      setLoading(false);
    }
  };
  const onChange = (orgs, org) => {
    // setPage(1);
    setOrgIds(() => orgs);
    setOrgName(org.name);
    initData(orgs[0]);
  };

  const handleAdd = () => {
    setVisible(true);
    setPageState((state) => ({ ...state, page_status: 1 }));
  };

  const handleDelete = async (record) => {
    const res = await deletePosition({
      pms_job_id: record.pms_job_id,
    });
    if (res.data.code === 0) {
      message.success("删除成功");
      initData(pageState.org_id);
    } else {
      message.error(res.data.message);
    }
  };

  const handleEdit = (record) => {
    setVisible(true);
    setPageState((state) => ({ ...state, page_status: 2 }));
    setPosition(record);
    setTimeout(() => {
      form.setFieldsValue(record);
    });
  };

  const onSearch = () => {
    setData([]); // 清空旧数据
    initData(pageState.org_id);
  };

  const onReset = () => {
    resetFields();
    onSearch();
  };

  const onExport = async () => {
    // setExportLoading(true);
    // const params = getFieldsValue();
    // const res = await exportAnnualEval({
    //   ...params,
    //   org_id: pageState.org_id,
    // });
    // setExportLoading(false);
  };

  const onInputFile = () => {
    // history.push(
    //   `/import-page?type=2&org_id=${pageState.org_id}&org_name=${pageState.org_name}`
    // );
  };

  const handleModalOk = async () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      setButtonStatus((state) => ({ ...state, modalSave: true }));
      const { page_status } = pageState;
      if (page_status === 1) {
        const res = await addPosition(values);
        if (res.data.code === 0) {
          message.success("新增成功");
          initData(pageState.org_id);
        } else {
          message.error(res.data.message);
        }
      } else if (page_status === 2) {
        const res = await updatePosition({
          ...values,
          pms_job_id: position.pms_job_id,
        });
        if (res.data.code === 0) {
          message.success("保存成功");
        } else {
          message.error(res.data.message);
        }
      }
      setButtonStatus((state) => ({ ...state, modalSave: false }));
      setVisible(false);
    });
  };

  const handleModalCancel = () => {
    form.resetFields();
    setButtonStatus((state) => ({ ...state, modalSave: false }));
    setVisible(false);
  };

  const formItemLayout = {
    labelCol: {
      span: 4,
    },
    wrapperCol: {
      span: 12,
    },
  };

  const columns = [
    {
      title: "分类",
      dataIndex: "type",
      key: "type",
      render: (type) => {
        const option = codeMap.classificationOption.find(
          (item) => item.op_key == type
        );
        return option ? option.op_value : "";
      },
    },
    {
      title: "职务全称",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "职务简称",
      dataIndex: "short_name",
      key: "short_name",
    },
    {
      title: "操作",
      key: "operation",
      render: (_, record) => (
        <div>
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => handleDelete(record)}
            okText="是"
            cancelText="否"
          >
            <Button type="link">删除</Button>
          </Popconfirm>
        </div>
      ),
    },
  ];

  return (
    <div className="position-management">
      <div className="org-tree-box">
        <OrgTree onChange={onChange} ref={(ref) => (orgRef.current = ref)} />
      </div>
      <div className="content-box">
        <div className="search-box">
          <Form layout="inline">
            <Form.Item label="姓名">
              {getFieldDecorator("user_name")(
                <Input style={{ width: "200px" }} placeholder="请输入" />
              )}
            </Form.Item>

            <Form.Item label="干部序列">
              {getFieldDecorator("group_id")(
                <Select
                  style={{ minWidth: "200px" }}
                  placeholder="请选择"
                ></Select>
              )}
            </Form.Item>
            <Form.Item>
              <Button type="primary" icon="search" onClick={onSearch}>
                查询
              </Button>
              <Button icon="redo" className="reset-btn" onClick={onReset}>
                重置
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={onExport} loading={exportLoading} disabled={true}>
            导出
          </Button>
        </div>
        <div className="add-box">
          <Button onClick={handleAdd} type="primary" icon="plus">
            添加职务
          </Button>
          <Button onClick={onInputFile} className="input-file" disabled={true}>
            导入
          </Button>
        </div>
        <Table
          bordered
          loading={loading}
          columns={columns}
          dataSource={data}
          rowKey="pms_job_id"
        />
      </div>
      <Modal
        title={pageState.page_status === 1 ? "添加职务" : "修改职务"}
        visible={visible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        footer={[
          <Button key="cancel" onClick={handleModalCancel}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={buttonStatus.modalSave}
            onClick={handleModalOk}
          >
            保存
          </Button>,
        ]}
      >
        <Form {...formItemLayout}>
          <FormItem label="分类">
            {getFieldDecorator("type", {
              initialValue: position.type,
            })(
              <Select disabled={pageState.page_status === 2}>
                {codeMap.classificationOption.map((item) => (
                  <Option key={item.op_key}>{item.op_value}</Option>
                ))}
              </Select>
            )}
          </FormItem>
          <FormItem label="职务全称">
            {getFieldDecorator("name", {
              initialValue: position.name,
            })(<Input placeholder="请输入" />)}
          </FormItem>
          <FormItem label="职务简称">
            {getFieldDecorator("short_name", {
              initialValue: position.short_name,
              rules: [{ required: true, message: "请填写职务简称" }],
            })(<Input placeholder="请输入" />)}
          </FormItem>
          <FormItem label="兼职全称">
            {getFieldDecorator("part_job_name", {
              initialValue: position.part_job_name,
            })(<Input placeholder="请输入" />)}
          </FormItem>
          <FormItem label="兼职简称">
            {getFieldDecorator("part_job_short_name", {
              initialValue: position.part_job_short_name,
            })(<Input placeholder="请输入" />)}
          </FormItem>
          <FormItem label="发文抬头">
            {getFieldDecorator("title", {
              initialValue: position.title,
            })(<Input placeholder="请输入" />)}
          </FormItem>
          <FormItem label="职务类别">
            {getFieldDecorator("category", {
              initialValue: position.category,
              rules: [{ required: true, message: "请选择职务类别" }],
            })(
              <Select>
                {codeMap.categoryOption.map((item) => (
                  <Option key={item.op_key}>{item.op_value}</Option>
                ))}
              </Select>
            )}
          </FormItem>
          <FormItem label="职务数量">
            {getFieldDecorator("num", {
              initialValue: position.num,
              rules: [{ required: true, message: "请填写职务数量" }],
            })(<InputNumber placeholder="请输入" />)}
          </FormItem>
        </Form>
      </Modal>
    </div>
  );
};

export default Form.create()(PositionManagement);
