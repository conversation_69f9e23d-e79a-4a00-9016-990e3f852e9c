.praise-commendation {
  width: 100%;
  height: 100%;
  display: flex;

  .left {
    display: flex;
    width: 310px;
    background: #fff;
    flex-direction: column;

    .left-header {
      width: 100%;
      height: 50px;
      background: #f1f5f8;

      text h2 {
        height: 50px;
        line-height: 50px;
        background: #fff;
        width: 100px;
        text-align: center;
      }

      .ant-tabs {
        height: 40px;
      }

      .ant-tabs-tab {
        margin: 0;
        height: 40px;
        line-height: 38px;
        padding: 0 16px;
        border: 1px solid #e8e8e8;
        border-radius: 4px 4px 0 0;
        margin-right: 2px;
      }

      .ant-tabs-tab-active {
        background: #fff;
      }

      .ant-tabs-ink-bar {
        display: none !important;
      }
    }

    .searchOrg {
      margin: 20px;
      // width: 300px;
      background: #f7f8f9;
    }

    .orgContainer {
      position: relative;
      overflow: auto;
      background: #f7f8f9;
      min-height: 400px;
      max-height: 600px;
    }
  }

  .right {
    flex: 1;
    margin-left: 10px;
    background: #fff;
    padding-bottom: 20px;
    position: relative;
    padding: 20px;

    .praise-header {
      .header-search {
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        .r-label {
          white-space: nowrap;
        }
        div {
          display: flex;
          align-items: center;
          margin-right: 30px;
          .ant-select-selection{
            width: 120px;
          }
        }

        .search-span {
          height: 100%;
          padding: 0px 10px;
          background: #fff;
          border: 1px solid #cdcdcd;
          margin-right: 5px;
          cursor: pointer;
        }

        .search-active {
          height: 100%;
          padding: 0px 10px;
          border: 1px solid #cdcdcd;
          margin-right: 5px;
          cursor: pointer;
          color: #fff;
          background: #a4adb3;
        }

        .search-btn {
          div {
            width: 80px;
            height: 30px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #1fa1fd;
            color: #fff;
            cursor: pointer;
          }

          .reset {
            color: #828a96;
            background: #f7f8fa;
          }
        }
      }

      .btn-item{
        display: flex;
        .import-btn{
          margin-left: 15px;
        }
      }
      
      .header-btn {
        width: 80px;
        height: 30px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #1fa1fd;
        color: #fff;
        cursor: pointer;

        i {
          margin-right: 10px;
        }
      }
    }

    .praise-content {
      margin-top: 10px;

      .handle {
        display: flex;
        justify-content: space-evenly;
      }
    }
  }
}
