
## 公安考核考评请切换到 cqga_appraisal 分支

# toG 项目
## 安装

安装：`npm i`

开发：`npm run dev` // 默认运行市直版本

建议运行

> npm start

## 全局定义



本项目已默认暴露的全局变量：`React`、`Component`、`Ant`

//已在webpack.ProvidePlugin中定义

所以头部不用引入这些包，下面代码是可以**省略**：

```javascript
import React, { Component } from "react";
import * as Ant from "antd";
```

当使用ant的组件时，需要在Ant对象里面取，例如：

`<Ant.Button type="primary">Primary</Ant.Button>`

## 文件夹说明
```
build/config               各系统构建打包环境配置

common/models              dva模型配置
common/router              路由信息，每增加页面需要引入路由配置

client/apis                后端接口全部在这里
client/components          公共组件
client/view                页面文件夹，每个路由一个子文件夹
client/tool                常用工具函数
client/tool/util.js        工具函数集
client/config              静态配置信息
client/config/map-menu.js  菜单映射配置/面包屑配置（和权限有关），配路由记得配置此文件
```


## 数据建模

当前页面是否需要使用dva？

  可以在创建页面前，建议先对几个业务关联页面进行分析后建模，如果多个页面有共享的数据的情况用dva，而简易的页面可以直接使用组件的state。为了保持系统简单，同一个字段尽量不要在dva里面定义了又在state里面定义。


## 网关接口约束

由于网关的原因，不让提交值为undefined字段。

解决方法可以自己判断提交的数据，如果为undefined, delete掉

还有一种方式： 在定义接口时直接使用 client/tool/axios 下的方法 filterParamsValue 使用方式[参考](https://git.goodsogood.com/pc-management/union_worksystem/src/dev/client/apis/activity.js#L35)

## 处理接口返回数据
项目使用axios作为ajax请求，要取到返回的业务数据，还要进行一些判断
通常需要下面这样写法:
```javascript
let response = yield getNewsListData();  //调用client/apis下面的接口
let { status, data: body } = response;
if (status === 200) {
      let { code, message: messageInfo, status, data } = body;
      if (code === 0 && messageInfo === 'success' && status === 200) {
          //TODO取出data
      }
}
```
每调用一次接口就要对状态 错误码 信息等判断，但这些判断是固定的。

所以，有另外一种方式：
```javascript
let response = yield getNewsListData();
let data = fetch(response);  //fetch 定义在 client/tool/axios
```
fetch 使用方式[参考](https://git.goodsogood.com/pc-management/union_worksystem/src/dev/client/view/integral-rule/index.js#L104)
fetch 方法把判断逻辑提取出来放到统一放到函数内部了

相关的还有 fetchOP fetchList 方法
* **fetch** 是标准的处理数据
* **fetchOP** 处理纯粹的操作只返回true false（新建 删除等操作）
* **fetchList** 是处理分页数据


## 表单双向绑定

示例: client/view/demo/index.js

## 自动发包
  * 上线打包注意：

  * 在根目录下面有个deploy.config.js是配置自动发包的文件

  * 使用gsg命令进行发包

    > npm i gsg-client -g

  * 发包的命令查看npm gsg-client api

  * 测试gsg d test

  * 预发布：gsg d preprod

  * 正式：gsg d master
    <!-- * 上线通过后在realse分支使用`gsg push master`并打上tag  -->
