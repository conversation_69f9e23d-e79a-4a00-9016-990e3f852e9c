{"name": "tog-admin", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build:test": "cross-env CUSTOM_ENV=93 ONLINE_ENV=test NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:release": "cross-env NODE_OPTIONS=--max-old-space-size=4096 CUSTOM_ENV=3 NODE_ENV=production ONLINE_ENV=release webpack", "build:master": "cross-env NODE_OPTIONS=--max-old-space-size=4096 CUSTOM_ENV=3 NODE_ENV=production ONLINE_ENV=master webpack", "build:analyz": "cross-env NODE_OPTIONS=--max-old-space-size=4096 CUSTOM_ENV=3 NODE_ENV=production ONLINE_ENV=test ANALYZ_ENV=1 webpack", "build:szf": "cross-env CUSTOM_ENV=3 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:saas": "cross-env CUSTOM_ENV=-1 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:spb": "cross-env CUSTOM_ENV=6 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:yunyang": "cross-env CUSTOM_ENV=7 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:youyang": "cross-env CUSTOM_ENV=8 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:jbz": "cross-env CUSTOM_ENV=9 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:yubei": "cross-env CUSTOM_ENV=10 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:wangan": "cross-env CUSTOM_ENV=11 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:cqa": "cross-env CUSTOM_ENV=14 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:shuihuan": "cross-env CUSTOM_ENV=21 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:liangjiang": "cross-env CUSTOM_ENV=22 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:fengdu": "cross-env CUSTOM_ENV=35 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "build:zygaosu": "cross-env CUSTOM_ENV=24 ONLINE_ENV=master NODE_ENV=production NODE_OPTIONS=--max-old-space-size=4096 webpack", "start": "node ./build/start.js"}, "author": "", "license": "ISC", "dependencies": {"@babel/polyfill": "^7.2.5", "@babel/runtime-corejs3": "^7.17.9", "@wangeditor/editor": "^5.0.0", "@wangeditor/editor-for-react": "^1.0.0", "antd": "^3.10.10", "archiver": "^7.0.1", "array-move": "^4.0.0", "autoprefixer": "^8.6.5", "axios": "^0.18.0", "babel-core": "^6.26.3", "babel-plugin-module-resolver": "^3.1.1", "babel-preset-react-hmre": "^1.1.1", "babel-register": "^6.26.0", "clean-webpack-plugin": "^3.0.0", "co": "^4.6.0", "cropperjs": "^1.5.12", "cross-env": "^5.1.6", "crypto-js": "^3.1.9-1", "dva": "^2.4.1", "echarts": "^5.3.0", "echarts-for-react": "^3.0.2", "file-saver": "^2.0.5", "gdt-jsapi": "^1.9.41", "history": "^4.7.2", "html-docx-js": "^0.3.1", "html-to-image": "^1.11.11", "html-webpack-plugin": "^4.0.2", "html2canvas": "^1.4.1", "immutability-helper": "^3.1.1", "immutable": "^3.8.2", "js-md5": "^0.7.3", "jszip": "^3.5.0", "lodash": "^4.17.11", "merge": "^1.2.1", "mini-css-extract-plugin": "^0.9.0", "moment": "^2.22.2", "optimize-css-assets-webpack-plugin": "^4.0.2", "postcss-loader": "^2.1.6", "qrcode": "^1.5.3", "qrcode.react": "^3.0.1", "qs": "^6.6.0", "react": "^16.14.0", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^16.14.0", "react-hot-loader": "^4.5.1", "react-sortable-hoc": "^2.0.0", "react-sortablejs": "6.0.0", "sortablejs": "^1.13.0", "speed-measure-webpack-plugin": "^1.3.3", "terser-webpack-plugin": "^2.3.5", "uuid": "^3.2.1", "webpack-bundle-analyzer": "^3.6.1", "webpack-dev-server": "^3.1.9", "webpack-manifest-plugin": "^2.0.4", "webpack-merge": "^4.1.4"}, "devDependencies": {"@types/sortablejs": "^1.10.6", "babel-eslint": "^10.1.0", "babel-loader": "^7.1.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-import": "^1.9.1", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-transform-regenerator": "^6.26.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-preset-env": "^1.7.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-preset-stage-3": "^6.24.1", "cache-loader": "^4.1.0", "copy-webpack-plugin": "^4.5.3", "css-loader": "^0.28.11", "eslint-config-ali": "^13.1.0", "execa": "^5.0.0", "file-loader": "^1.1.11", "hard-source-webpack-plugin": "^0.13.1", "inquirer": "^7.3.3", "inquirer-autocomplete-prompt": "^1.3.0", "jsdom": "^13.2.0", "json-loader": "^0.5.7", "jsonwebtoken": "^8.5.1", "less": "^3.8.1", "less-loader": "^4.1.0", "node-cmd": "^4.0.0", "open-browser-webpack-plugin": "^0.0.5", "progress-bar-webpack-plugin": "^1.11.0", "save": "^2.3.2", "style-loader": "^0.21.0", "thread-loader": "^2.1.3", "url-loader": "^1.1.2", "webpack": "^4.26.0", "webpack-build-notifier": "^2.0.0", "webpack-cli": "^3.0.0", "webstorm-disable-index": "^1.2.0"}}